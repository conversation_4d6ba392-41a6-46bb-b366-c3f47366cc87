package com.vueappmobile.data.repository

import com.vueappmobile.data.database.TaskDao
import com.vueappmobile.data.model.Task
import kotlinx.coroutines.flow.Flow

class TaskRepository(
    private val taskDao: TaskDao
) {
    fun getAllTasks(): Flow<List<Task>> = taskDao.getAllTasks()

    suspend fun getTaskById(id: Long): Task? = taskDao.getTaskById(id)

    fun getTasksByStatus(isCompleted: Boolean): Flow<List<Task>> = 
        taskDao.getTasksByStatus(isCompleted)

    suspend fun insertTask(task: Task): Long = taskDao.insertTask(task)

    suspend fun updateTask(task: Task) = taskDao.updateTask(task)

    suspend fun deleteTask(task: Task) = taskDao.deleteTask(task)

    suspend fun deleteTaskById(id: Long) = taskDao.deleteTaskById(id)

    suspend fun updateTaskStatus(id: Long, isCompleted: Boolean) = 
        taskDao.updateTaskStatus(id, isCompleted)
}
