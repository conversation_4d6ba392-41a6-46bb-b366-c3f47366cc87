package com.vueappmobile.ui.theme

import androidx.compose.ui.graphics.Color

// iOS-inspired color palette
val IOSBlue = Color(0xFF007AFF)
val IOSGreen = Color(0xFF34C759)
val IOSRed = Color(0xFFFF3B30)
val IOSOrange = Color(0xFFFF9500)
val IOSYellow = Color(0xFFFFCC00)
val IOSPurple = Color(0xFFAF52DE)
val IOSPink = Color(0xFFFF2D92)
val IOSTeal = Color(0xFF5AC8FA)

// System colors
val IOSSystemBackground = Color(0xFFFFFFFF)
val IOSSecondarySystemBackground = Color(0xFFF2F2F7)
val IOSTertiarySystemBackground = Color(0xFFFFFFFF)
val IOSSystemGroupedBackground = Color(0xFFF2F2F7)
val IOSSecondarySystemGroupedBackground = Color(0xFFFFFFFF)

// Label colors
val IOSLabel = Color(0xFF000000)
val IOSSecondaryLabel = Color(0xFF3C3C43).copy(alpha = 0.6f)
val IOSTertiaryLabel = Color(0xFF3C3C43).copy(alpha = 0.3f)

// Separator colors
val IOSSeparator = Color(0xFF3C3C43).copy(alpha = 0.29f)
val IOSOpaqueSeparator = Color(0xFFC6C6C8)

// Dark mode colors
val IOSSystemBackgroundDark = Color(0xFF000000)
val IOSSecondarySystemBackgroundDark = Color(0xFF1C1C1E)
val IOSTertiarySystemBackgroundDark = Color(0xFF2C2C2E)
val IOSSystemGroupedBackgroundDark = Color(0xFF000000)
val IOSSecondarySystemGroupedBackgroundDark = Color(0xFF1C1C1E)

val IOSLabelDark = Color(0xFFFFFFFF)
val IOSSecondaryLabelDark = Color(0xFFEBEBF5).copy(alpha = 0.6f)
val IOSTertiaryLabelDark = Color(0xFFEBEBF5).copy(alpha = 0.3f)
