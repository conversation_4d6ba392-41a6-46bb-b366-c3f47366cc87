package com.vueappmobile.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vueappmobile.data.model.TaskPriority
import com.vueappmobile.ui.components.*
import com.vueappmobile.ui.theme.*
import com.vueappmobile.ui.viewmodel.TaskViewModel

@Composable
fun CreateTaskScreen(
    viewModel: TaskViewModel,
    onNavigateBack: () -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf(TaskPriority.MEDIUM) }
    
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(IOSSystemGroupedBackground)
    ) {
        IOSTopBar(
            title = "New Task",
            navigationIcon = Icons.Default.ArrowBack,
            onNavigationClick = onNavigateBack,
            actions = {
                IOSTextButton(
                    text = "Save",
                    onClick = {
                        if (title.isNotBlank()) {
                            viewModel.createTask(title, description, selectedPriority)
                            onNavigateBack()
                        }
                    },
                    enabled = title.isNotBlank()
                )
            }
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Title input
            IOSCard {
                IOSTextField(
                    value = title,
                    onValueChange = { title = it },
                    placeholder = "Task title",
                    label = "Title",
                    imeAction = ImeAction.Next
                )
            }
            
            // Description input
            IOSCard {
                IOSTextField(
                    value = description,
                    onValueChange = { description = it },
                    placeholder = "Add a description...",
                    label = "Description",
                    singleLine = false,
                    maxLines = 5,
                    imeAction = ImeAction.Done
                )
            }
            
            // Priority selection
            IOSCard {
                Column {
                    Text(
                        text = "Priority",
                        style = MaterialTheme.typography.bodyMedium,
                        color = IOSSecondaryLabel,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    TaskPriority.values().forEach { priority ->
                        PriorityItem(
                            priority = priority,
                            isSelected = selectedPriority == priority,
                            onSelect = { selectedPriority = priority }
                        )
                        
                        if (priority != TaskPriority.values().last()) {
                            Spacer(modifier = Modifier.height(12.dp))
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Create button
            IOSButton(
                text = "Create Task",
                onClick = {
                    if (title.isNotBlank()) {
                        viewModel.createTask(title, description, selectedPriority)
                        onNavigateBack()
                    }
                },
                enabled = title.isNotBlank()
            )
        }
    }
}

@Composable
private fun PriorityItem(
    priority: TaskPriority,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable { onSelect() }
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(
                    Color(priority.color),
                    CircleShape
                )
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = priority.displayName,
            fontSize = 17.sp,
            fontWeight = FontWeight.Medium,
            color = IOSLabel,
            modifier = Modifier.weight(1f)
        )
        
        if (isSelected) {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(IOSBlue, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(IOSSystemBackground, CircleShape)
                )
            }
        } else {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(IOSSecondaryLabel.copy(alpha = 0.3f), CircleShape)
            )
        }
    }
}
