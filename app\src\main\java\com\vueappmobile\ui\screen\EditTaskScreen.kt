package com.vueappmobile.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vueappmobile.data.model.Task
import com.vueappmobile.data.model.TaskPriority
import com.vueappmobile.ui.components.*
import com.vueappmobile.ui.theme.*
import com.vueappmobile.ui.viewmodel.TaskViewModel

@Composable
fun EditTaskScreen(
    taskId: Long,
    viewModel: TaskViewModel,
    onNavigateBack: () -> Unit
) {
    var task by remember { mutableStateOf<Task?>(null) }
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf(TaskPriority.MEDIUM) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    val scrollState = rememberScrollState()

    // Load task data
    LaunchedEffect(taskId) {
        val loadedTask = viewModel.getTaskById(taskId)
        loadedTask?.let {
            task = it
            title = it.title
            description = it.description
            selectedPriority = it.priority
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(IOSSystemGroupedBackground)
    ) {
        IOSTopBar(
            title = "Edit Task",
            navigationIcon = Icons.Default.ArrowBack,
            onNavigationClick = onNavigateBack,
            actions = {
                IconButton(onClick = { showDeleteDialog = true }) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete Task",
                        tint = IOSRed
                    )
                }
                IOSTextButton(
                    text = "Save",
                    onClick = {
                        task?.let { currentTask ->
                            val updatedTask = currentTask.copy(
                                title = title.trim(),
                                description = description.trim(),
                                priority = selectedPriority
                            )
                            viewModel.updateTask(updatedTask)
                            onNavigateBack()
                        }
                    },
                    enabled = title.isNotBlank() && task != null
                )
            }
        )
        
        if (task != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // Title input
                IOSCard {
                    IOSTextField(
                        value = title,
                        onValueChange = { title = it },
                        placeholder = "Task title",
                        label = "Title",
                        imeAction = ImeAction.Next
                    )
                }
                
                // Description input
                IOSCard {
                    IOSTextField(
                        value = description,
                        onValueChange = { description = it },
                        placeholder = "Add a description...",
                        label = "Description",
                        singleLine = false,
                        maxLines = 5,
                        imeAction = ImeAction.Done
                    )
                }
                
                // Priority selection
                IOSCard {
                    Column {
                        Text(
                            text = "Priority",
                            style = MaterialTheme.typography.bodyMedium,
                            color = IOSSecondaryLabel,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        
                        TaskPriority.values().forEach { priority ->
                            PriorityItem(
                                priority = priority,
                                isSelected = selectedPriority == priority,
                                onSelect = { selectedPriority = priority }
                            )
                            
                            if (priority != TaskPriority.values().last()) {
                                Spacer(modifier = Modifier.height(12.dp))
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // Update button
                IOSButton(
                    text = "Update Task",
                    onClick = {
                        task?.let { currentTask ->
                            val updatedTask = currentTask.copy(
                                title = title.trim(),
                                description = description.trim(),
                                priority = selectedPriority
                            )
                            viewModel.updateTask(updatedTask)
                            onNavigateBack()
                        }
                    },
                    enabled = title.isNotBlank()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Delete button
                IOSDestructiveButton(
                    text = "Delete Task",
                    onClick = { showDeleteDialog = true }
                )
            }
        } else {
            // Loading state
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = IOSBlue)
            }
        }
    }
    
    // Delete confirmation dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = {
                Text(
                    text = "Delete Task",
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = {
                Text("Are you sure you want to delete this task? This action cannot be undone.")
            },
            confirmButton = {
                IOSTextButton(
                    text = "Delete",
                    onClick = {
                        task?.let { viewModel.deleteTask(it) }
                        showDeleteDialog = false
                        onNavigateBack()
                    },
                    color = IOSRed
                )
            },
            dismissButton = {
                IOSTextButton(
                    text = "Cancel",
                    onClick = { showDeleteDialog = false }
                )
            }
        )
    }
}

@Composable
private fun PriorityItem(
    priority: TaskPriority,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable { onSelect() }
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(
                    Color(priority.color),
                    CircleShape
                )
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = priority.displayName,
            fontSize = 17.sp,
            fontWeight = FontWeight.Medium,
            color = IOSLabel,
            modifier = Modifier.weight(1f)
        )
        
        if (isSelected) {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(IOSBlue, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(IOSSystemBackground, CircleShape)
                )
            }
        } else {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(IOSSecondaryLabel.copy(alpha = 0.3f), CircleShape)
            )
        }
    }
}
