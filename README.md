# VueAppMobile - iOS-Style CRUD App

A beautiful Android CRUD application built with Kotlin and Jetpack Compose, featuring an iOS-inspired design system.

## 🚀 Features

- **Complete CRUD Operations**: Create, Read, Update, and Delete tasks
- **iOS-Style UI**: Beautiful, modern interface inspired by iOS design principles
- **Material Design 3**: Latest Material Design components with custom iOS-style theming
- **Room Database**: Local data persistence with SQLite
- **MVVM Architecture**: Clean architecture with ViewModels and Repository pattern
- **Jetpack Compose**: Modern declarative UI toolkit
- **Navigation Component**: Seamless navigation between screens

## 📱 Screenshots

The app features:
- Task list with filtering (All, Pending, Completed)
- Create new tasks with priority levels
- Edit existing tasks
- Delete tasks with confirmation
- iOS-style cards, buttons, and navigation

## 🛠️ Tech Stack

- **Language**: Kotlin
- **UI Framework**: Jetpack Compose
- **Architecture**: MVVM (Model-View-ViewModel)
- **Database**: Room (SQLite)
- **Navigation**: Navigation Compose
- **Dependency Injection**: Manual DI (easily extensible to Hilt/Dagger)
- **Coroutines**: For asynchronous operations

## 🏗️ Project Structure

```
app/src/main/java/com/vueappmobile/
├── data/
│   ├── database/          # Room database, DAOs, converters
│   ├── model/            # Data models
│   └── repository/       # Repository pattern implementation
├── navigation/           # Navigation setup
├── ui/
│   ├── components/       # Reusable iOS-style UI components
│   ├── screen/          # App screens (List, Create, Edit)
│   ├── theme/           # iOS-inspired theme and colors
│   └── viewmodel/       # ViewModels for state management
└── MainActivity.kt      # Main activity
```

## 🎨 Design System

The app implements an iOS-inspired design system with:

- **Colors**: iOS system colors (Blue, Green, Red, etc.)
- **Typography**: iOS-style font weights and sizes
- **Components**: Custom iOS-style cards, buttons, text fields
- **Navigation**: iOS-style navigation bars and transitions

## 🚀 Getting Started

1. Clone the repository
2. Open in Android Studio
3. Sync Gradle files
4. Run the app on an emulator or device

## 📋 Requirements

- Android Studio Arctic Fox or later
- Android SDK 24+
- Kotlin 1.9.10+

## 🔧 Build Configuration

The app uses:
- Compile SDK: 34
- Min SDK: 24
- Target SDK: 34
- Kotlin Compiler Extension: 1.5.4

## 📝 License

This project is open source and available under the MIT License.
