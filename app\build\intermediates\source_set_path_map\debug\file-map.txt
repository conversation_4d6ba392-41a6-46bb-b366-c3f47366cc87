com.vueappmobile.app-core-1.12.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\074d7bed37a35c62f498056aebbebfc5\transformed\core-1.12.0\res
com.vueappmobile.app-navigation-common-ktx-2.7.5-1 C:\Users\<USER>\.gradle\caches\transforms-3\081fc5dbe4080e47160db1755f2bea84\transformed\navigation-common-ktx-2.7.5\res
com.vueappmobile.app-navigation-common-2.7.5-2 C:\Users\<USER>\.gradle\caches\transforms-3\14bfd861fdc2cdf3c01ed3fa8d27378d\transformed\navigation-common-2.7.5\res
com.vueappmobile.app-lifecycle-viewmodel-savedstate-2.7.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\1834bd0ffccb89cb9a7677c2fccc8804\transformed\lifecycle-viewmodel-savedstate-2.7.0\res
com.vueappmobile.app-customview-poolingcontainer-1.0.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\18bd19171adb12b4cbe09083a0a32a27\transformed\customview-poolingcontainer-1.0.0\res
com.vueappmobile.app-ui-graphics-release-5 C:\Users\<USER>\.gradle\caches\transforms-3\18c0d7132eee1f981f786cc0ce37749f\transformed\ui-graphics-release\res
com.vueappmobile.app-annotation-experimental-1.3.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\26b8bc11d16baf737358196c439564fc\transformed\annotation-experimental-1.3.0\res
com.vueappmobile.app-sqlite-framework-2.4.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\2a9643e501d95f99b75f576deeac3888\transformed\sqlite-framework-2.4.0\res
com.vueappmobile.app-room-ktx-2.6.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\2c642ed5501e325cda1622933d928ccf\transformed\room-ktx-2.6.1\res
com.vueappmobile.app-navigation-compose-2.7.5-9 C:\Users\<USER>\.gradle\caches\transforms-3\3bf0f733e534133cefd2d1bb4f99bfdf\transformed\navigation-compose-2.7.5\res
com.vueappmobile.app-lifecycle-process-2.7.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\462503fd85f1a369839f3f687b9aae8f\transformed\lifecycle-process-2.7.0\res
com.vueappmobile.app-runtime-release-11 C:\Users\<USER>\.gradle\caches\transforms-3\4ea5027ed9bb14f7af1d9bd09739bc08\transformed\runtime-release\res
com.vueappmobile.app-ui-release-12 C:\Users\<USER>\.gradle\caches\transforms-3\51571bb2ed6fcfe05c6944152125937d\transformed\ui-release\res
com.vueappmobile.app-ui-tooling-release-13 C:\Users\<USER>\.gradle\caches\transforms-3\517eaf6b27115f17d65d115638544b23\transformed\ui-tooling-release\res
com.vueappmobile.app-lifecycle-viewmodel-ktx-2.7.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\536ab2656fcaea2d9d2113e1a8230d6a\transformed\lifecycle-viewmodel-ktx-2.7.0\res
com.vueappmobile.app-ui-test-manifest-1.5.4-15 C:\Users\<USER>\.gradle\caches\transforms-3\62abe41508b0c2f2c90cb09ba499a1d5\transformed\ui-test-manifest-1.5.4\res
com.vueappmobile.app-foundation-release-16 C:\Users\<USER>\.gradle\caches\transforms-3\7744ff36f367443293d8a32ccc7fac71\transformed\foundation-release\res
com.vueappmobile.app-room-runtime-2.6.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\7b088e060610f382cd87ef37e6aaeeaf\transformed\room-runtime-2.6.1\res
com.vueappmobile.app-core-ktx-1.12.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\7b19d5449222780a93fe592d00fa1208\transformed\core-ktx-1.12.0\res
com.vueappmobile.app-runtime-saveable-release-19 C:\Users\<USER>\.gradle\caches\transforms-3\7cdeeaeb1164b58f7cc2a4c0961bfd38\transformed\runtime-saveable-release\res
com.vueappmobile.app-savedstate-1.2.1-20 C:\Users\<USER>\.gradle\caches\transforms-3\830be869fa93b8a2bfde0d071c66dc27\transformed\savedstate-1.2.1\res
com.vueappmobile.app-material-icons-core-release-21 C:\Users\<USER>\.gradle\caches\transforms-3\860dbfaba5b4011f1b04f37b70bcc322\transformed\material-icons-core-release\res
com.vueappmobile.app-ui-text-release-22 C:\Users\<USER>\.gradle\caches\transforms-3\89df9067dad7a3fa64d5c3e579925088\transformed\ui-text-release\res
com.vueappmobile.app-ui-unit-release-23 C:\Users\<USER>\.gradle\caches\transforms-3\92928dc1737c9e8529ada1c86872342e\transformed\ui-unit-release\res
com.vueappmobile.app-activity-1.8.2-24 C:\Users\<USER>\.gradle\caches\transforms-3\935cde534c1c1b03cc5b9bd561215325\transformed\activity-1.8.2\res
com.vueappmobile.app-ui-tooling-data-release-25 C:\Users\<USER>\.gradle\caches\transforms-3\94392caa70de3d88b722cd9f6a92b53b\transformed\ui-tooling-data-release\res
com.vueappmobile.app-core-runtime-2.2.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\a28df973290ecf9e673c9f24b538ed87\transformed\core-runtime-2.2.0\res
com.vueappmobile.app-material3-1.1.2-27 C:\Users\<USER>\.gradle\caches\transforms-3\a2d08c03c2192f56a901e619275bd6e2\transformed\material3-1.1.2\res
com.vueappmobile.app-emoji2-1.4.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\a6393ac24e8b3eabf68f3a52c634ef24\transformed\emoji2-1.4.0\res
com.vueappmobile.app-lifecycle-livedata-core-2.7.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\a946998bb433ef0e4b1ab874c24128fa\transformed\lifecycle-livedata-core-2.7.0\res
com.vueappmobile.app-material-ripple-release-30 C:\Users\<USER>\.gradle\caches\transforms-3\b5c78509a51566a016dd5958983dda2a\transformed\material-ripple-release\res
com.vueappmobile.app-lifecycle-runtime-2.7.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\be677755c041c9b405d7572c4b8f4137\transformed\lifecycle-runtime-2.7.0\res
com.vueappmobile.app-animation-release-32 C:\Users\<USER>\.gradle\caches\transforms-3\bfe9a2c13ecbf1ffff3901d5ad1ed950\transformed\animation-release\res
com.vueappmobile.app-navigation-runtime-ktx-2.7.5-33 C:\Users\<USER>\.gradle\caches\transforms-3\c4228c1bfb774d55f34e01658ef4f62b\transformed\navigation-runtime-ktx-2.7.5\res
com.vueappmobile.app-activity-compose-1.8.2-34 C:\Users\<USER>\.gradle\caches\transforms-3\c684502fff9374904751d497180a6488\transformed\activity-compose-1.8.2\res
com.vueappmobile.app-savedstate-ktx-1.2.1-35 C:\Users\<USER>\.gradle\caches\transforms-3\c8850a8dafc8a502481db13b0bf9b11f\transformed\savedstate-ktx-1.2.1\res
com.vueappmobile.app-startup-runtime-1.1.1-36 C:\Users\<USER>\.gradle\caches\transforms-3\c95a8f7ae180f992346f623c910f6756\transformed\startup-runtime-1.1.1\res
com.vueappmobile.app-ui-tooling-preview-release-37 C:\Users\<USER>\.gradle\caches\transforms-3\cba546287e658a981edf538c759d1773\transformed\ui-tooling-preview-release\res
com.vueappmobile.app-material-release-38 C:\Users\<USER>\.gradle\caches\transforms-3\d027f42d5884ff1e781b4ce82dbc114e\transformed\material-release\res
com.vueappmobile.app-animation-core-release-39 C:\Users\<USER>\.gradle\caches\transforms-3\d65b907d8403133cdce9101db8c9d321\transformed\animation-core-release\res
com.vueappmobile.app-foundation-layout-release-40 C:\Users\<USER>\.gradle\caches\transforms-3\d6959cbdef835e6daafca3c09b5c2f24\transformed\foundation-layout-release\res
com.vueappmobile.app-ui-geometry-release-41 C:\Users\<USER>\.gradle\caches\transforms-3\d858e7e0dbf8cf2b7282aa09acd69a7f\transformed\ui-geometry-release\res
com.vueappmobile.app-lifecycle-runtime-ktx-2.7.0-42 C:\Users\<USER>\.gradle\caches\transforms-3\e29c8d5d445861427925957bc56ba5cd\transformed\lifecycle-runtime-ktx-2.7.0\res
com.vueappmobile.app-lifecycle-viewmodel-compose-2.7.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\ece9e6fdf0263f14a44b51bf5040daad\transformed\lifecycle-viewmodel-compose-2.7.0\res
com.vueappmobile.app-lifecycle-viewmodel-2.7.0-44 C:\Users\<USER>\.gradle\caches\transforms-3\f0ff1075bb2ccb847e03ad2f7b78b7ae\transformed\lifecycle-viewmodel-2.7.0\res
com.vueappmobile.app-material-icons-extended-release-45 C:\Users\<USER>\.gradle\caches\transforms-3\f154314a95072adfc4d7227b810d266f\transformed\material-icons-extended-release\res
com.vueappmobile.app-navigation-runtime-2.7.5-46 C:\Users\<USER>\.gradle\caches\transforms-3\f20bde0368fa9e69ba7c1f5928527ffc\transformed\navigation-runtime-2.7.5\res
com.vueappmobile.app-sqlite-2.4.0-47 C:\Users\<USER>\.gradle\caches\transforms-3\f2b74a7e54e270425c1274820655dc86\transformed\sqlite-2.4.0\res
com.vueappmobile.app-profileinstaller-1.3.0-48 C:\Users\<USER>\.gradle\caches\transforms-3\fdcf287e4ceeb30407ad0f73ca6bea9e\transformed\profileinstaller-1.3.0\res
com.vueappmobile.app-ui-util-release-49 C:\Users\<USER>\.gradle\caches\transforms-3\feb33609b3746c09b769de4a0c5befcc\transformed\ui-util-release\res
com.vueappmobile.app-activity-ktx-1.8.2-50 C:\Users\<USER>\.gradle\caches\transforms-3\ff159a67dd044e4312269e5a6ff53133\transformed\activity-ktx-1.8.2\res
com.vueappmobile.app-resValues-51 E:\VueAppMobile\app\build\generated\res\resValues\debug
com.vueappmobile.app-packageDebugResources-52 E:\VueAppMobile\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.vueappmobile.app-packageDebugResources-53 E:\VueAppMobile\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.vueappmobile.app-merged_res-54 E:\VueAppMobile\app\build\intermediates\merged_res\debug
com.vueappmobile.app-debug-55 E:\VueAppMobile\app\src\debug\res
com.vueappmobile.app-main-56 E:\VueAppMobile\app\src\main\res
