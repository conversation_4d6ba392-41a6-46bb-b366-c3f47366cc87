package com.vueappmobile

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.rememberNavController
import com.vueappmobile.data.database.TaskDatabase
import com.vueappmobile.data.repository.TaskRepository
import com.vueappmobile.navigation.TaskNavigation
import com.vueappmobile.ui.theme.VueAppMobileTheme
import com.vueappmobile.ui.viewmodel.TaskViewModel
import com.vueappmobile.ui.viewmodel.TaskViewModelFactory

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Initialize database and repository
        val database = TaskDatabase.getDatabase(this)
        val repository = TaskRepository(database.taskDao())
        val viewModelFactory = TaskViewModelFactory(repository)
        
        setContent {
            VueAppMobileTheme {
                val navController = rememberNavController()
                val viewModel: TaskViewModel = viewModel(factory = viewModelFactory)
                
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    TaskNavigation(
                        navController = navController,
                        viewModel = viewModel
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    VueAppMobileTheme {
        // Preview content
    }
}
